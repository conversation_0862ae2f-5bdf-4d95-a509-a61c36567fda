import { useState } from 'react';
import './Sidebar.css';

const Sidebar = ({ activeItem, onItemClick }) => {
  const [isCollapsed, setIsCollapsed] = useState(false);

  const menuItems = [
    {
      id: 'CD-LPMT',
      label: 'CD-LPMT',
      hasChildren: true,
      children: [
        { id: 'CD-LPMT-0', label: '0' },
        { id: 'CD-LPMT-1', label: '1' },
        { id: 'CD-LPMT-2', label: '2' },
        { id: 'CD-LPMT-3', label: '3' },
        { id: 'CD-LPMT-4', label: '4' },
        { id: 'CD-LPMT-5', label: '5' },
        { id: 'CD-LPMT-6', label: '6' },
        { id: 'CD-LPMT-7', label: '7' },
        { id: 'CD-LPMT-8', label: '8' },
        { id: 'CD-LPMT-9', label: '9' },
      ]
    },
    { id: 'WP-LPMT', label: 'WP-LPMT'},
    { id: 'SPMT-T/Q', label: 'SPMT-T/Q'},
    { id: 'TT', label: 'TT'},
    { id: 'CD-T/Q', label: 'CD-T/Q'},
    { id: 'WP-T/Q', label: 'WP-T/Q'},
    { id: 'MM(Multi-Message)', label: 'MM(Multi-Message)'},
    { id: 'LOW_E', label: 'LOW_E'},
    { id: 'TQ_TAO_CD', label: 'TQ_TAO_CD'},
    { id: 'TQ_TAO_TVT', label: 'TQ_TAO_TVT'},
    { id: 'TQ_TAO_WT', label: 'TQ_TAO_WT'},
    { id: 'TQ_TAO_CD_FEC', label: 'TQ_TAO_CD_FEC'},
  ];

  const handleToggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };

  return (
    <div className="sidebar sidebar--open">
      <div className="sidebar__header">
        <div className="sidebar__logo">
          <span className="sidebar__logo-text">单事例监测系统</span>
        </div>
      </div>
      
      <nav className="sidebar__nav">
        <ul className="sidebar__menu">
          {menuItems.map((item) => (
            <li key={item.id} className="sidebar__menu-item">
              {item.hasChildren ? (
                <>
                  <button
                    className={`sidebar__menu-link sidebar__menu-link--parent ${
                      activeItem === item.id || activeItem.startsWith(item.id + '-') ? 'sidebar__menu-link--active' : ''
                    }`}
                    onClick={handleToggleCollapse}
                    title={item.label}
                  >
                    <span className="sidebar__menu-text">{item.label}</span>
                    <span className={`sidebar__menu-arrow ${isCollapsed ? 'sidebar__menu-arrow--collapsed' : ''}`}>
                      ▼
                    </span>
                  </button>
                  <ul className={`sidebar__submenu ${isCollapsed ? 'sidebar__submenu--collapsed' : ''}`}>
                    {item.children.map((child) => (
                      <li key={child.id} className="sidebar__submenu-item">
                        <button
                          className={`sidebar__submenu-link ${
                            activeItem === child.id ? 'sidebar__submenu-link--active' : ''
                          }`}
                          onClick={() => onItemClick(child.id)}
                          title={child.label}
                        >
                          <span className="sidebar__submenu-text">{child.label}</span>
                        </button>
                      </li>
                    ))}
                  </ul>
                </>
              ) : (
                <button
                  className={`sidebar__menu-link ${
                    activeItem === item.id ? 'sidebar__menu-link--active' : ''
                  }`}
                  onClick={() => onItemClick(item.id)}
                  title={item.label}
                >
                  <span className="sidebar__menu-text">{item.label}</span>
                </button>
              )}
            </li>
          ))}
        </ul>
      </nav>
      
      {/* <div className="sidebar__footer">
        <div className="sidebar__user">
          <div className="sidebar__user-avatar">👤</div>
          <div className="sidebar__user-info">
            <div className="sidebar__user-name">管理员</div>
          </div>
        </div>
      </div> */}
    </div>
  );
};

export default Sidebar;

.layout {
  display: flex;
  height: 100vh;
  background-color: #f8f9fa;
}

.layout__main {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
  margin-left: 280px;
  width: calc(100vw - 280px);
  height: 100vh;
}

.layout__header {
  background: white;
  border-bottom: 1px solid #e9ecef;
  padding: 0 30px;
  height: 70px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 0;
  z-index: 100;
}

.layout__header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.layout__title {
  margin: 0;
  display: flex;
  align-items: center;
}

.data-assembled-id {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  background: #f8f9fa;
  padding: 8px 16px;
  border-radius: 6px;
  border: 1px solid #dee2e6;
  font-family: 'Courier New', monospace;
}

.layout__header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.layout__header-search {
  position: relative;
  margin-right: 16px;
}

.layout__header-input {
  width: 450px;
  height: 40px;
  padding: 0 16px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  background: white;
}

.layout__header-input:focus {
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.layout__header-input::placeholder {
  color: #adb5bd;
}

/* 图标按钮样式 - 与通知按钮一致 */
.layout__icon-btn {
  background: none;
  border: 2px solid transparent;
  padding: 8px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 18px;
  transition: all 0.2s ease;
  position: relative;
  outline: none !important;
  -webkit-tap-highlight-color: transparent;
}

.layout__icon-btn:hover {
  background-color: #f8f9fa;
}

.layout__icon-btn:focus {
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
  outline: none !important;
}

.layout__icon-btn:active {
  outline: none !important;
}

/* 记录按钮激活状态的脉冲效果 */
.layout__record-btn--active {
  animation: record-pulse 2s infinite;
}

@keyframes record-pulse {
  0%, 100% {
    background-color: transparent;
  }
  50% {
    background-color: rgba(231, 76, 60, 0.1);
  }
}

.layout__notification-btn,
.layout__profile-btn {
  background: none;
  border: 2px solid transparent;
  padding: 8px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 18px;
  transition: all 0.2s ease;
  position: relative;
  outline: none !important;
  -webkit-tap-highlight-color: transparent;
}

.layout__notification-btn:hover,
.layout__profile-btn:hover {
  background-color: #f8f9fa;
}

.layout__notification-btn:focus,
.layout__profile-btn:focus {
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
  outline: none !important;
}

.layout__notification-btn:active,
.layout__profile-btn:active {
  outline: none !important;
}

.layout__notification-badge {
  position: absolute;
  top: 5px;
  right: 5px;
  background: #e74c3c;
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
}

.layout__content {
  flex: 1;
  padding: 30px;
  overflow-y: auto;
  background-color: #f8f9fa;
}

/* 仪表板样式 */
.dashboard__cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.dashboard__card {
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 20px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.dashboard__card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.dashboard__card-icon {
  font-size: 40px;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #3498db, #2980b9);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.dashboard__card-content h3 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #7f8c8d;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.dashboard__card-number {
  margin: 0;
  font-size: 28px;
  font-weight: 700;
  color: #2c3e50;
}

.dashboard__chart {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dashboard__chart h3 {
  margin: 0 0 20px 0;
  font-size: 18px;
  color: #2c3e50;
  font-weight: 600;
}

.dashboard__chart-placeholder {
  height: 300px;
  background: #f8f9fa;
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6c757d;
  font-size: 16px;
  text-align: center;
}

/* 通用页面内容样式 */
.page-content {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-content h2 {
  margin: 0 0 20px 0;
  font-size: 24px;
  color: #2c3e50;
  font-weight: 600;
}

.page-content p {
  color: #6c757d;
  line-height: 1.6;
  margin: 0;
}

/* 新的内容布局样式 */
.content-layout {
  display: flex;
  flex-direction: column;
  gap: 24px;
  height: 100%;
}

.content-top {
  display: grid;
  grid-template-columns: 1.6fr 1fr;
  gap: 20px;
  min-height: 300px;
}

.content-bottom {
  flex: 1;
  min-height: 400px;
}

.content-block {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 24px;
  border: 1px solid #e9ecef;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.content-block h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  color: #2c3e50;
  font-weight: 600;
  border-bottom: 2px solid #3498db;
  padding-bottom: 8px;
}

.content-block h4 {
  margin: 16px 0 12px 0;
  font-size: 16px;
  color: #2c3e50;
  font-weight: 600;
}

.content-block p {
  color: #6c757d;
  line-height: 1.6;
  margin-bottom: 16px;
}

.content-block--full {
  width: 100%;
}

/* 系统信息样式 */
.system-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 16px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #dee2e6;
}

.info-label {
  font-weight: 500;
  color: #495057;
  font-size: 14px;
}

.info-value {
  font-weight: 600;
  color: #2c3e50;
  font-size: 14px;
}

/* 分析内容样式 */
.analysis-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
  margin-top: 16px;
  flex: 1;
}

.analysis-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.chart-placeholder {
  height: 200px;
  background: #f8f9fa;
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6c757d;
  font-size: 16px;
  margin-top: 12px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
  margin-top: 12px;
}

.metric-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  text-align: center;
}

.metric-label {
  font-size: 12px;
  color: #6c757d;
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.metric-value {
  font-size: 20px;
  font-weight: 700;
  color: #2c3e50;
}

/* 数据流输出框样式 */
.data-stream-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: 12px;
}

.data-stream-output {
  flex: 1;
  background: #1e1e1e;
  border-radius: 8px;
  padding: 16px;
  font-family: 'Courier New', 'Monaco', monospace;
  font-size: 13px;
  line-height: 1.4;
  overflow-y: auto;
  max-height: 300px;
  border: 1px solid #333;
}

.stream-line {
  display: flex;
  gap: 8px;
  margin-bottom: 4px;
  align-items: flex-start;
}

.stream-line.paused {
  background: rgba(255, 193, 7, 0.1);
  padding: 2px 4px;
  border-radius: 4px;
}

.stream-line.recording {
  background: rgba(220, 53, 69, 0.1);
  padding: 2px 4px;
  border-radius: 4px;
  animation: recording-glow 2s infinite;
}

@keyframes recording-glow {
  0%, 100% {
    background: rgba(220, 53, 69, 0.1);
  }
  50% {
    background: rgba(220, 53, 69, 0.2);
  }
}

.timestamp {
  color: #6c757d;
  white-space: nowrap;
  min-width: 140px;
}

.data-type {
  color: #fff;
  font-weight: bold;
  min-width: 50px;
  text-align: center;
  padding: 1px 6px;
  border-radius: 3px;
  font-size: 11px;
}

.data-type:contains("INFO") {
  background: #17a2b8;
}

.stream-line:has(.data-type:contains("INFO")) .data-type {
  background: #17a2b8;
}

.stream-line:has(.data-type:contains("DATA")) .data-type {
  background: #28a745;
}

.stream-line:has(.data-type:contains("WARN")) .data-type {
  background: #ffc107;
  color: #000;
}

.stream-line:has(.data-type:contains("PAUSE")) .data-type {
  background: #6c757d;
}

.stream-line:has(.data-type:contains("REC")) .data-type {
  background: #dc3545;
}

.message {
  color: #e9ecef;
  flex: 1;
  word-break: break-word;
}

.stream-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: white;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.stream-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #28a745;
}

.status-indicator.paused {
  background: #ffc107;
}

.status-indicator.active {
  background: #28a745;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.status-text {
  font-size: 14px;
  font-weight: 500;
  color: #495057;
}

.stream-stats {
  display: flex;
  gap: 16px;
}

.stat-item {
  font-size: 13px;
  color: #6c757d;
  font-family: 'Courier New', monospace;
}

/* 报警信息输出框样式 */
.alert-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: 12px;
}

.alert-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: white;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.alert-stats {
  display: flex;
  gap: 16px;
}

.alert-count {
  font-size: 13px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.alert-count.error {
  background: #f8d7da;
  color: #721c24;
}

.alert-count.warning {
  background: #fff3cd;
  color: #856404;
}

.clear-alerts-btn {
  background: none;
  border: none;
  padding: 6px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  transition: background-color 0.2s ease;
}

.clear-alerts-btn:hover {
  background: #f8f9fa;
}

.alerts-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
  overflow-y: auto;
  max-height: 280px;
  padding-right: 4px;
}

.alert-item {
  display: flex;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  border-left: 4px solid;
  background: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  cursor: pointer;
}

.alert-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.alert-error {
  border-left-color: #dc3545;
  background: linear-gradient(90deg, rgba(220, 53, 69, 0.05) 0%, white 20%);
}

.alert-warning {
  border-left-color: #ffc107;
  background: linear-gradient(90deg, rgba(255, 193, 7, 0.05) 0%, white 20%);
}

.alert-icon {
  font-size: 18px;
  margin-top: 2px;
  flex-shrink: 0;
}

.alert-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.alert-title {
  font-weight: 600;
  font-size: 14px;
  color: #2c3e50;
  line-height: 1.2;
}

.alert-message {
  font-size: 13px;
  color: #6c757d;
  line-height: 1.4;
  margin-bottom: 4px;
}

.alert-time {
  font-size: 11px;
  color: #adb5bd;
  font-family: 'Courier New', monospace;
  align-self: flex-end;
}

/* 滚动条样式 */
.alerts-list::-webkit-scrollbar {
  width: 6px;
}

.alerts-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.alerts-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.alerts-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Header输入框内容显示样式 */
.header-input-display {
  margin-top: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #3498db;
}

.header-input-display h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  color: #2c3e50;
}

.input-value {
  margin: 0;
  font-family: 'Courier New', monospace;
  background: white;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid #dee2e6;
  color: #495057;
  font-weight: 500;
}

/* 控制状态显示样式 */
.control-status-display {
  margin-top: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #17a2b8;
}

.control-status-display h3 {
  margin: 0 0 12px 0;
  font-size: 16px;
  color: #2c3e50;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #dee2e6;
}

.status-label {
  font-weight: 500;
  color: #495057;
  font-size: 14px;
}

.status-value {
  font-weight: 600;
  font-size: 14px;
  padding: 4px 8px;
  border-radius: 4px;
  text-transform: uppercase;
  font-size: 12px;
  letter-spacing: 0.5px;
}

.status-running {
  background: #d4edda;
  color: #155724;
}

.status-paused {
  background: #fff3cd;
  color: #856404;
}

.status-recording {
  background: #f8d7da;
  color: #721c24;
  animation: status-pulse 2s infinite;
}

.status-stopped {
  background: #e2e3e5;
  color: #383d41;
}

@keyframes status-pulse {
  0%, 100% {
    background: #f8d7da;
  }
  50% {
    background: #f5c6cb;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .layout__main {
    margin-left: 0;
  }
  
  .layout__header {
    padding: 0 20px;
  }
  
  .layout__content {
    padding: 20px;
  }
  
  .layout__title {
    font-size: 20px;
  }

  .layout__header-input {
    width: 250px;
  }
  
  .dashboard__cards {
    grid-template-columns: 1fr;
  }
  
  .dashboard__card {
    padding: 20px;
  }
  
  .dashboard__card-icon {
    font-size: 32px;
    width: 50px;
    height: 50px;
  }
  
  .dashboard__card-number {
    font-size: 24px;
  }

  /* 内容布局响应式 */
  .content-top {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .content-block {
    padding: 20px;
  }

  .metrics-grid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 12px;
  }

  .chart-placeholder {
    height: 150px;
    font-size: 14px;
  }

  /* 数据流输出框响应式 */
  .data-stream-output {
    font-size: 12px;
    padding: 12px;
    max-height: 250px;
  }

  .timestamp {
    min-width: 120px;
    font-size: 11px;
  }

  .data-type {
    min-width: 45px;
    font-size: 10px;
  }

  .stream-controls {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  .stream-stats {
    gap: 12px;
  }

  /* 报警信息框响应式 */
  .alert-header {
    padding: 10px 12px;
  }

  .alert-stats {
    gap: 12px;
  }

  .alert-count {
    font-size: 12px;
    padding: 3px 6px;
  }

  .alerts-list {
    max-height: 220px;
  }

  .alert-item {
    padding: 10px;
    gap: 10px;
  }

  .alert-icon {
    font-size: 16px;
  }

  .alert-title {
    font-size: 13px;
  }

  .alert-message {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .layout__header-actions {
    gap: 10px;
  }

  .layout__header-input {
    width: 160px;
    font-size: 13px;
  }

  .layout__notification-btn,
  .layout__profile-btn {
    padding: 8px;
    font-size: 16px;
  }

  .dashboard__card {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }

  /* 小屏幕内容布局 */
  .content-layout {
    gap: 16px;
  }

  .content-block {
    padding: 16px;
  }

  .content-block h3 {
    font-size: 16px;
  }

  .analysis-content {
    gap: 16px;
  }

  .analysis-section {
    padding: 16px;
  }

  .metric-item {
    padding: 12px;
  }

  .metric-value {
    font-size: 18px;
  }

  /* 小屏幕数据流优化 */
  .data-stream-output {
    font-size: 11px;
    padding: 10px;
    max-height: 200px;
  }

  .timestamp {
    min-width: 100px;
    font-size: 10px;
  }

  .message {
    font-size: 11px;
  }

  .stream-controls {
    padding: 10px 12px;
  }

  .status-text {
    font-size: 13px;
  }

  .stat-item {
    font-size: 12px;
  }

  /* 小屏幕报警信息优化 */
  .alert-header {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
    padding: 8px 10px;
  }

  .alert-stats {
    gap: 8px;
  }

  .alert-count {
    font-size: 11px;
  }

  .alerts-list {
    max-height: 180px;
  }

  .alert-item {
    padding: 8px;
    gap: 8px;
  }

  .alert-title {
    font-size: 12px;
  }

  .alert-message {
    font-size: 11px;
  }

  .alert-time {
    font-size: 10px;
  }
}



import { useState } from 'react';
import Sidebar from '../sidebar/Sidebar';
import './Layout.css';

const Layout = ({ children }) => {
  const [activeMenuItem, setActiveMenuItem] = useState('CD-LPMT');
  const [headerInputValue, setHeaderInputValue] = useState('');
  const [isPaused, setIsPaused] = useState(false);
  const [isRecording, setIsRecording] = useState(false);

  const handleMenuItemClick = (itemId) => {
    setActiveMenuItem(itemId);
  };



  const handleHeaderInputChange = (e) => {
    setHeaderInputValue(e.target.value);
    console.log('Header输入框内容:', e.target.value);
  };



  // 处理暂停/开始按钮点击
  const handlePauseToggle = () => {
    setIsPaused(!isPaused);
    console.log(isPaused ? '开始数据流' : '暂停数据流');
  };

  // 处理记录按钮点击
  const handleRecordToggle = () => {
    setIsRecording(!isRecording);
    console.log(isRecording ? '停止记录' : '开始记录');
  };

  return (
    <div className="layout">
      <Sidebar
        activeItem={activeMenuItem}
        onItemClick={handleMenuItemClick}
      />

      <div className="layout__main">
        <header className="layout__header">
          <div className="layout__header-content">
            <div className="layout__title">
              <span className="data-assembled-id">Data assembled id：31</span>
            </div>
            <div className="layout__header-actions">
              <div className="layout__header-search">
                <input
                  type="text"
                  className="layout__header-input"
                  placeholder="过滤数据流..."
                  value={headerInputValue}
                  onChange={handleHeaderInputChange}
                />
              </div>

              <button
                className="layout__icon-btn layout__pause-btn"
                onClick={handlePauseToggle}
                title={isPaused ? '开始数据流' : '暂停数据流'}
              >
                {isPaused ? '▶️' : '⏸️'}
              </button>

              <button
                className={`layout__icon-btn layout__record-btn ${isRecording ? 'layout__record-btn--active' : ''}`}
                onClick={handleRecordToggle}
                title={isRecording ? '停止记录' : '开始记录'}
              >
                {isRecording ? '⏹️' : '🔴'}
              </button>

              <button className="layout__notification-btn" title="通知">
                🔔
                <span className="layout__notification-badge">5</span>
              </button>
            </div>
          </div>
        </header>
        
        <main className="layout__content">
          {children || <DefaultContent
            activeItem={activeMenuItem}
            isPaused={isPaused}
            isRecording={isRecording}
          />}
        </main>
      </div>
    </div>
  );
};

const getPageTitle = (activeItem) => {
  const titles = {
    'CD-LPMT': 'CD-LPMT',
    'CD-LPMT-0': 'CD-LPMT - 0',
    'CD-LPMT-1': 'CD-LPMT - 1',
    'CD-LPMT-2': 'CD-LPMT - 2',
    'CD-LPMT-3': 'CD-LPMT - 3',
    'CD-LPMT-4': 'CD-LPMT - 4',
    'CD-LPMT-5': 'CD-LPMT - 5',
    'CD-LPMT-6': 'CD-LPMT - 6',
    'CD-LPMT-7': 'CD-LPMT - 7',
    'CD-LPMT-8': 'CD-LPMT - 8',
    'CD-LPMT-9': 'CD-LPMT - 9',
    'WP-LPMT': 'WP-LPMT',
    'SPMT-T/Q': 'SPMT-T/Q',
    'TT': 'TT',
    'CD-T/Q': 'CD-T/Q',
    'WP-T/Q': 'WP-T/Q',
    'MM(Multi-Message)': 'MM(Multi-Message)',
    'LOW_E': 'LOW_E',
    'TQ_TAO_CD': 'TQ_TAO_CD',
    'TQ_TAO_TVT': 'TQ_TAO_TVT',
    'TQ_TAO_WT': 'TQ_TAO_WT',
    'TQ_TAO_CD_FEC': 'TQ_TAO_CD_FEC',
  };
  return titles[activeItem] || 'CD-LPMT';
};

const DefaultContent = ({ activeItem, isPaused, isRecording }) => {
  const content = {
    'CD-LPMT': (
      <div className="page-content">
        <div className="content-layout">
          {/* 上半部分：两个块 */}
          <div className="content-top">
            <div className="content-block content-block--left">
              <h3>数据流</h3>
              <div className="data-stream-container">
                <div className="data-stream-output">
                  <div className="stream-line">
                    <span className="timestamp">[2024-01-15 14:23:01]</span>
                    <span className="data-type">INFO</span>
                    <span className="message">数据流连接已建立</span>
                  </div>
                  <div className="stream-line">
                    <span className="timestamp">[2024-01-15 14:23:02]</span>
                    <span className="data-type">DATA</span>
                    <span className="message">接收数据包: {"{"}"id": 1001, "value": 42.5, "status": "normal"{"}"}</span>
                  </div>
                  <div className="stream-line">
                    <span className="timestamp">[2024-01-15 14:23:03]</span>
                    <span className="data-type">DATA</span>
                    <span className="message">接收数据包: {"{"}"id": 1002, "value": 38.2, "status": "normal"{"}"}</span>
                  </div>
                  <div className="stream-line">
                    <span className="timestamp">[2024-01-15 14:23:04]</span>
                    <span className="data-type">WARN</span>
                    <span className="message">数据延迟检测: 延迟 150ms</span>
                  </div>
                  <div className="stream-line">
                    <span className="timestamp">[2024-01-15 14:23:05]</span>
                    <span className="data-type">DATA</span>
                    <span className="message">接收数据包: {"{"}"id": 1003, "value": 45.1, "status": "normal"{"}"}</span>
                  </div>
                  <div className="stream-line">
                    <span className="timestamp">[2024-01-15 14:23:06]</span>
                    <span className="data-type">INFO</span>
                    <span className="message">数据处理完成，共处理 3 条记录</span>
                  </div>
                  {isPaused && (
                    <div className="stream-line paused">
                      <span className="timestamp">[2024-01-15 14:23:07]</span>
                      <span className="data-type">PAUSE</span>
                      <span className="message">数据流已暂停</span>
                    </div>
                  )}
                  {isRecording && (
                    <div className="stream-line recording">
                      <span className="timestamp">[2024-01-15 14:23:08]</span>
                      <span className="data-type">REC</span>
                      <span className="message">正在记录数据流...</span>
                    </div>
                  )}
                </div>
                <div className="stream-controls">
                  <div className="stream-status">
                    <span className={`status-indicator ${isPaused ? 'paused' : 'active'}`}></span>
                    <span className="status-text">
                      {isPaused ? '数据流已暂停' : '输出正常'}
                    </span>
                  </div>
                  <div className="stream-stats">
                    <span className="stat-item">速率: 2.3 msg/s</span>
                    <span className="stat-item">总计: 1,247 条</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="content-block content-block--right">
              <h3>报警信息</h3>
              <div className="alert-container">
                <div className="alert-header">
                  <div className="alert-stats">
                    <span className="alert-count error">错误: 2</span>
                    <span className="alert-count warning">警告: 3</span>
                  </div>
                  <button className="clear-alerts-btn" title="清除所有报警">
                    🗑️
                  </button>
                </div>

                <div className="alerts-list">
                  <div className="alert-item alert-error">
                    <div className="alert-icon">❌</div>
                    <div className="alert-content">
                      <div className="alert-title">redis连接失败</div>
                      <div className="alert-message">无法连接到redis获取数据</div>
                      <div className="alert-time">14:23:15</div>
                    </div>
                  </div>

                  <div className="alert-item alert-warning">
                    <div className="alert-icon">⚠️</div>
                    <div className="alert-content">
                      <div className="alert-title">通道数异常</div>
                      <div className="alert-message">事例中通道数为5</div>
                      <div className="alert-time">14:22:48</div>
                    </div>
                  </div>

                  <div className="alert-item alert-error">
                    <div className="alert-icon">❌</div>
                    <div className="alert-content">
                      <div className="alert-title">网络超时</div>
                      <div className="alert-message">API请求超时</div>
                      <div className="alert-time">14:21:32</div>
                    </div>
                  </div>

                  <div className="alert-item alert-warning">
                    <div className="alert-icon">⚠️</div>
                    <div className="alert-content">
                      <div className="alert-title">磁盘空间不足</div>
                      <div className="alert-message">系统磁盘剩余空间仅有 2.1GB</div>
                      <div className="alert-time">14:20:15</div>
                    </div>
                  </div>

                  <div className="alert-item alert-warning">
                    <div className="alert-icon">⚠️</div>
                    <div className="alert-content">
                      <div className="alert-title">CPU温度异常</div>
                      <div className="alert-message">CPU温度达到 78°C，接近警告阈值</div>
                      <div className="alert-time">14:19:42</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 下半部分：一个块 */}
          <div className="content-bottom">
            <div className="content-block content-block--full">
              <h3>CD-LPMT - ROOT图</h3>
              <p>这里是CD-LPMT的详细监测内容和分析结果。您可以在这里查看完整的数据分析、图表展示和历史记录。</p>
              <div className="analysis-content">
                <div className="analysis-section">
                  <h4>数据趋势</h4>
                  <div className="chart-placeholder">
                    📈 图表区域 - 数据趋势分析
                  </div>
                </div>
                <div className="analysis-section">
                  <h4>关键指标</h4>
                  <div className="metrics-grid">
                    <div className="metric-item">
                      <span className="metric-label">通道数</span>
                      <span className="metric-value">10</span>
                    </div>
                    <div className="metric-item">
                      <span className="metric-label">时间戳</span>
                      <span className="metric-value">99.8%</span>
                    </div>
                    <div className="metric-item">
                      <span className="metric-label">错误数量</span>
                      <span className="metric-value">3</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    ),
    'CD-LPMT-0': (
      <div className="page-content">
        <h2>CD-LPMT - 0</h2>
        <p>这里是CD-LPMT子项0的监测内容。您可以在这里查看相关的监测数据和分析结果。</p>
      </div>
    ),
    'CD-LPMT-1': (
      <div className="page-content">
        <h2>CD-LPMT - 1</h2>
        <p>这里是CD-LPMT子项1的监测内容。您可以在这里查看相关的监测数据和分析结果。</p>
      </div>
    ),
    'CD-LPMT-2': (
      <div className="page-content">
        <h2>CD-LPMT - 2</h2>
        <p>这里是CD-LPMT子项2的监测内容。您可以在这里查看相关的监测数据和分析结果。</p>
      </div>
    ),
    'CD-LPMT-3': (
      <div className="page-content">
        <h2>CD-LPMT - 3</h2>
        <p>这里是CD-LPMT子项3的监测内容。您可以在这里查看相关的监测数据和分析结果。</p>
      </div>
    ),
    'CD-LPMT-4': (
      <div className="page-content">
        <h2>CD-LPMT - 4</h2>
        <p>这里是CD-LPMT子项4的监测内容。您可以在这里查看相关的监测数据和分析结果。</p>
      </div>
    ),
    'CD-LPMT-5': (
      <div className="page-content">
        <h2>CD-LPMT - 5</h2>
        <p>这里是CD-LPMT子项5的监测内容。您可以在这里查看相关的监测数据和分析结果。</p>
      </div>
    ),
    'CD-LPMT-6': (
      <div className="page-content">
        <h2>CD-LPMT - 6</h2>
        <p>这里是CD-LPMT子项6的监测内容。您可以在这里查看相关的监测数据和分析结果。</p>
      </div>
    ),
    'CD-LPMT-7': (
      <div className="page-content">
        <h2>CD-LPMT - 7</h2>
        <p>这里是CD-LPMT子项7的监测内容。您可以在这里查看相关的监测数据和分析结果。</p>
      </div>
    ),
    'CD-LPMT-8': (
      <div className="page-content">
        <h2>CD-LPMT - 8</h2>
        <p>这里是CD-LPMT子项8的监测内容。您可以在这里查看相关的监测数据和分析结果。</p>
      </div>
    ),
    'CD-LPMT-9': (
      <div className="page-content">
        <h2>CD-LPMT - 9</h2>
        <p>这里是CD-LPMT子项9的监测内容。您可以在这里查看相关的监测数据和分析结果。</p>
      </div>
    ),
    'WP-LPMT': (
      <div className="page-content">
        <h2>WP-LPMT</h2>
        <p>这里是WP-LPMT的监测内容。您可以在这里查看相关的监测数据和分析结果。</p>
      </div>
    ),
    'SPMT-T/Q': (
      <div className="page-content">
        <h2>SPMT-T/Q</h2>
        <p>这里是SPMT-T/Q的监测内容。您可以在这里查看相关的监测数据和分析结果。</p>
      </div>
    ),
    'TT': (
      <div className="page-content">
        <h2>TT</h2>
        <p>这里是TT的监测内容。您可以在这里查看相关的监测数据和分析结果。</p>
      </div>
    ),
    'CD-T/Q': (
      <div className="page-content">
        <h2>CD-T/Q</h2>
        <p>这里是CD-T/Q的监测内容。您可以在这里查看相关的监测数据和分析结果。</p>
      </div>
    ),
    'WP-T/Q': (
      <div className="page-content">
        <h2>WP-T/Q</h2>
        <p>这里是WP-T/Q的监测内容。您可以在这里查看相关的监测数据和分析结果。</p>
      </div>
    ),
    'MM(Multi-Message)': (
      <div className="page-content">
        <h2>MM(Multi-Message)</h2>
        <p>这里是MM(Multi-Message)的监测内容。您可以在这里查看相关的监测数据和分析结果。</p>
      </div>
    ),
    'LOW_E': (
      <div className="page-content">
        <h2>LOW_E</h2>
        <p>这里是LOW_E的监测内容。您可以在这里查看相关的监测数据和分析结果。</p>
      </div>
    ),
    'TQ_TAO_CD': (
      <div className="page-content">
        <h2>TQ_TAO_CD</h2>
        <p>这里是TQ_TAO_CD的监测内容。您可以在这里查看相关的监测数据和分析结果。</p>
      </div>
    ),
    'TQ_TAO_TVT': (
      <div className="page-content">
        <h2>TQ_TAO_TVT</h2>
        <p>这里是TQ_TAO_TVT的监测内容。您可以在这里查看相关的监测数据和分析结果。</p>
      </div>
    ),
    'TQ_TAO_WT': (
      <div className="page-content">
        <h2>TQ_TAO_WT</h2>
        <p>这里是TQ_TAO_WT的监测内容。您可以在这里查看相关的监测数据和分析结果。</p>
      </div>
    ),
    'TQ_TAO_CD_FEC': (
      <div className="page-content">
        <h2>TQ_TAO_CD_FEC</h2>
        <p>这里是TQ_TAO_CD_FEC的监测内容。您可以在这里查看相关的监测数据和分析结果。</p>
      </div>
    ),
  };

  return content[activeItem] || content['CD-LPMT'];
};

export default Layout;

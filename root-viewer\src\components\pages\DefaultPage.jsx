import './DefaultPage.css';

const DefaultPage = () => {
  return (
    <div className="default-page">
      <div className="default-page__header">
        <h2>示例页面</h2>
        <p>这是一个示例页面，展示如何在侧边导航布局中添加自定义内容。</p>
      </div>
      
      <div className="default-page__content">
        <div className="default-page__section">
          <h3>功能特点</h3>
          <ul>
            <li>响应式侧边导航栏</li>
            <li>可折叠/展开的侧边栏</li>
            <li>现代化的UI设计</li>
            <li>移动端友好</li>
            <li>易于扩展和自定义</li>
          </ul>
        </div>
        
        <div className="default-page__section">
          <h3>使用方法</h3>
          <p>要在布局中添加自定义页面，您可以：</p>
          <ol>
            <li>创建新的页面组件</li>
            <li>在Layout组件中添加路由逻辑</li>
            <li>更新侧边栏菜单项</li>
            <li>添加相应的样式</li>
          </ol>
        </div>
        
        <div className="default-page__cards">
          <div className="default-card">
            <div className="default-card__icon">🎨</div>
            <h4>美观设计</h4>
            <p>现代化的界面设计，提供良好的用户体验</p>
          </div>
          
          <div className="default-card">
            <div className="default-card__icon">📱</div>
            <h4>响应式</h4>
            <p>完美适配各种屏幕尺寸，从桌面到移动设备</p>
          </div>
          
          <div className="default-card">
            <div className="default-card__icon">⚡</div>
            <h4>高性能</h4>
            <p>优化的代码结构，确保快速加载和流畅交互</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DefaultPage;

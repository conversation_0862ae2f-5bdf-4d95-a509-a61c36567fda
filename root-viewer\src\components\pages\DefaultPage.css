.default-page {
  max-width: 1200px;
  margin: 0 auto;
}

.default-page__header {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.default-page__header h2 {
  margin: 0 0 15px 0;
  font-size: 28px;
  color: #2c3e50;
  font-weight: 600;
}

.default-page__header p {
  margin: 0;
  color: #6c757d;
  font-size: 16px;
  line-height: 1.6;
}

.default-page__content {
  display: grid;
  gap: 30px;
}

.default-page__section {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.default-page__section h3 {
  margin: 0 0 20px 0;
  font-size: 20px;
  color: #2c3e50;
  font-weight: 600;
}

.default-page__section ul,
.default-page__section ol {
  margin: 0;
  padding-left: 20px;
  color: #6c757d;
  line-height: 1.8;
}

.default-page__section li {
  margin-bottom: 8px;
}

.default-page__section p {
  margin: 0 0 15px 0;
  color: #6c757d;
  line-height: 1.6;
}

.default-page__cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.default-card {
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.default-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.default-card__icon {
  font-size: 48px;
  margin-bottom: 15px;
}

.default-card h4 {
  margin: 0 0 15px 0;
  font-size: 18px;
  color: #2c3e50;
  font-weight: 600;
}

.default-card p {
  margin: 0;
  color: #6c757d;
  line-height: 1.6;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .default-page__header,
  .default-page__section {
    padding: 20px;
  }
  
  .default-page__header h2 {
    font-size: 24px;
  }
  
  .default-page__cards {
    grid-template-columns: 1fr;
  }
  
  .default-card {
    padding: 20px;
  }
}
